<section
    <?php echo e($attributes->merge(['class' => "@container flex flex-col p-6 sm:p-12 bg-white dark:bg-gray-900/80 text-gray-900 dark:text-gray-100 rounded-lg default:col-span-full default:lg:col-span-6 default:row-span-1 dark:ring-1 dark:ring-gray-800 shadow-xl"])); ?>

>
    <?php echo e($slot); ?>

</section>
<?php /**PATH C:\Users\<USER>\Documents\__GIT_Abdou\affiliate-app\affiliate-app-vue-laravel-version\affiliate-prod-ts-vue-laravel\starter-kit\vendor\laravel\framework\src\Illuminate\Foundation\Providers/../resources/exceptions/renderer/components/card.blade.php ENDPATH**/ ?>