[2025-07-26 22:33:55] local.ERROR: Database file at path [affiliate_db] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select * from "sessions" where "id" = gRivAyGcS7yJdtuIBmyA03fJKWv7ojkcos1wIVxU limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): Database file at path [affiliate_db] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: select * from \"sessions\" where \"id\" = gRivAyGcS7yJdtuIBmyA03fJKWv7ojkcos1wIVxU limit 1) at C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from \"...', Array, false)
#3 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3027): Illuminate\\Database\\Query\\Builder->first(Array)
#8 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(96): Illuminate\\Database\\Query\\Builder->find('gRivAyGcS7yJdtu...')
#9 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(116): Illuminate\\Session\\DatabaseSessionHandler->read('gRivAyGcS7yJdtu...')
#10 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(104): Illuminate\\Session\\Store->readFromHandler()
#11 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(88): Illuminate\\Session\\Store->loadSession()
#12 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(146): Illuminate\\Session\\Store->start()
#13 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#14 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(143): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#15 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(115): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#16 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#17 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#53 {main}

[previous exception] [object] (Illuminate\\Database\\SQLiteDatabaseDoesNotExistException(code: 0): Database file at path [affiliate_db] does not exist. Ensure this is an absolute path to the database. at C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php:59)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php(19): Illuminate\\Database\\Connectors\\SQLiteConnector->parseDatabasePath(false)
#1 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(223): Illuminate\\Database\\Connectors\\SQLiteConnector->connect(Array)
#2 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#3 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1228): call_user_func(Object(Closure))
#4 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(509): Illuminate\\Database\\Connection->getPdo()
#5 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): Illuminate\\Database\\Connection->getPdoForSelect(false)
#6 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from \"...', Array)
#7 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#8 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#9 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from \"...', Array, false)
#10 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#11 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#12 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#14 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3027): Illuminate\\Database\\Query\\Builder->first(Array)
#15 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(96): Illuminate\\Database\\Query\\Builder->find('gRivAyGcS7yJdtu...')
#16 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(116): Illuminate\\Session\\DatabaseSessionHandler->read('gRivAyGcS7yJdtu...')
#17 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(104): Illuminate\\Session\\Store->readFromHandler()
#18 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(88): Illuminate\\Session\\Store->loadSession()
#19 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(146): Illuminate\\Session\\Store->start()
#20 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#21 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(143): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#22 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(115): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#23 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#24 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#33 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#60 {main}
"} 
[2025-07-26 22:34:12] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'affiliate_db.sessions' doesn't exist (Connection: mysql, SQL: select * from `sessions` where `id` = gRivAyGcS7yJdtuIBmyA03fJKWv7ojkcos1wIVxU limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'affiliate_db.sessions' doesn't exist (Connection: mysql, SQL: select * from `sessions` where `id` = gRivAyGcS7yJdtuIBmyA03fJKWv7ojkcos1wIVxU limit 1) at C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#3 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3027): Illuminate\\Database\\Query\\Builder->first(Array)
#8 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(96): Illuminate\\Database\\Query\\Builder->find('gRivAyGcS7yJdtu...')
#9 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(116): Illuminate\\Session\\DatabaseSessionHandler->read('gRivAyGcS7yJdtu...')
#10 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(104): Illuminate\\Session\\Store->readFromHandler()
#11 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(88): Illuminate\\Session\\Store->loadSession()
#12 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(146): Illuminate\\Session\\Store->start()
#13 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#14 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(143): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#15 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(115): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#16 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#17 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#53 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'affiliate_db.sessions' doesn't exist at C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select * from `...')
#1 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#5 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3027): Illuminate\\Database\\Query\\Builder->first(Array)
#10 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(96): Illuminate\\Database\\Query\\Builder->find('gRivAyGcS7yJdtu...')
#11 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(116): Illuminate\\Session\\DatabaseSessionHandler->read('gRivAyGcS7yJdtu...')
#12 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(104): Illuminate\\Session\\Store->readFromHandler()
#13 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(88): Illuminate\\Session\\Store->loadSession()
#14 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(146): Illuminate\\Session\\Store->start()
#15 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#16 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(143): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#17 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(115): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#18 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#19 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#55 {main}
"} 
[2025-07-27 00:23:49] local.ERROR: Command "oprimize" is not defined.

Did you mean one of these?
    optimize
    optimize:clear {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"oprimize\" is not defined.

Did you mean one of these?
    optimize
    optimize:clear at C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\symfony\\console\\Application.php:726)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('oprimize')
#1 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#5 {main}
"} 
[2025-07-27 00:34:13] local.ERROR: Vite manifest not found at: C:\Users\<USER>\Documents\__GIT_Abdou\affiliate-app\affiliate-app-vue-laravel-version\affiliate-prod-ts-vue-laravel\starter-kit\public\build/manifest.json (View: C:\Users\<USER>\Documents\__GIT_Abdou\affiliate-app\affiliate-app-vue-laravel-version\affiliate-prod-ts-vue-laravel\starter-kit\resources\views\application.blade.php) {"exception":"[object] (Illuminate\\View\\ViewException(code: 0): Vite manifest not found at: C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\public\\build/manifest.json (View: C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\resources\\views\\application.blade.php) at C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php:934)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(Illuminate\\Foundation\\ViteManifestNotFoundException), 1)
#1 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#2 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#3 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#6 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#7 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#8 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#9 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#10 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#19 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#55 {main}

[previous exception] [object] (Illuminate\\Foundation\\ViteManifestNotFoundException(code: 0): Vite manifest not found at: C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\public\\build/manifest.json at C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php:934)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php(384): Illuminate\\Foundation\\Vite->manifest('build')
#1 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\storage\\framework\\views\\d023a82dcab90bb6bb50a0bb11b236a3.php(9): Illuminate\\Foundation\\Vite->__invoke(Object(Illuminate\\Support\\Collection))
#2 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#3 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#5 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#6 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#7 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#8 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#9 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#10 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#11 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(924): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#12 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#13 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#14 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#23 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#32 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#55 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Documents\\__GIT_Abdou\\affiliate-app\\affiliate-app-vue-laravel-version\\affiliate-prod-ts-vue-laravel\\starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#59 {main}
"} 
