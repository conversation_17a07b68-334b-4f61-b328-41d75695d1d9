{"name": "laravel/sanctum", "description": "Laravel Sanctum provides a featherweight authentication system for SPAs and simple APIs.", "keywords": ["laravel", "sanctum", "auth"], "license": "MIT", "support": {"issues": "https://github.com/laravel/sanctum/issues", "source": "https://github.com/laravel/sanctum"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.2", "ext-json": "*", "illuminate/console": "^11.0|^12.0", "illuminate/contracts": "^11.0|^12.0", "illuminate/database": "^11.0|^12.0", "illuminate/support": "^11.0|^12.0", "symfony/console": "^7.0"}, "require-dev": {"mockery/mockery": "^1.6", "orchestra/testbench": "^9.0|^10.0", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^11.3"}, "autoload": {"psr-4": {"Laravel\\Sanctum\\": "src/"}}, "autoload-dev": {"psr-4": {"Laravel\\Sanctum\\Tests\\": "tests/", "Workbench\\App\\": "workbench/app/", "Workbench\\Database\\Factories\\": "workbench/database/factories/"}}, "extra": {"laravel": {"providers": ["Laravel\\Sanctum\\SanctumServiceProvider"]}}, "config": {"sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true}